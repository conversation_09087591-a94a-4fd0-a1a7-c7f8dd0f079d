{"name": "spring-manufacturing-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "lucide-react": "^0.453.0", "motion": "^12.23.5", "next": "^15.0.0", "nodemailer": "^6.9.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "resend": "^3.2.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "15.0.0", "postcss": "^8", "tailwindcss": "^3.4.0", "typescript": "^5"}}