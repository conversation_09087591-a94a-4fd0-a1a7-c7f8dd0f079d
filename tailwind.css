@tailwind components;
@tailwind utilities;

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}

:root {
  --heading-h1-font-family: "Archivo", Helvetica;
  --heading-h1-font-size: 48px;
  --heading-h1-font-style: normal;
  --heading-h1-font-weight: 400;
  --heading-h1-letter-spacing: -0.5600000000000002px;
  --heading-h1-line-height: 120.00000762939453%;
  --heading-h2-font-family: "Archivo", Helvetica;
  --heading-h2-font-size: 48px;
  --heading-h2-font-style: normal;
  --heading-h2-font-weight: 400;
  --heading-h2-letter-spacing: -0.48px;
  --heading-h2-line-height: 120.00000762939453%;
  --heading-h4-font-family: "Archivo", Helvetica;
  --heading-h4-font-size: 32px;
  --heading-h4-font-style: normal;
  --heading-h4-font-weight: 400;
  --heading-h4-letter-spacing: -0.32px;
  --heading-h4-line-height: 130%;
  --heading-h5-font-family: "Archivo", Helvetica;
  --heading-h5-font-size: 24px;
  --heading-h5-font-style: normal;
  --heading-h5-font-weight: 400;
  --heading-h5-letter-spacing: -0.24px;
  --heading-h5-line-height: 140%;
  --heading-tagline-font-family: "Lexend Deca", Helvetica;
  --heading-tagline-font-size: 16px;
  --heading-tagline-font-style: normal;
  --heading-tagline-font-weight: 600;
  --heading-tagline-letter-spacing: 0px;
  --heading-tagline-line-height: 150%;
  --text-large-semi-bold-font-family: "Lexend Deca", Helvetica;
  --text-large-semi-bold-font-size: 20px;
  --text-large-semi-bold-font-style: normal;
  --text-large-semi-bold-font-weight: 600;
  --text-large-semi-bold-letter-spacing: 0px;
  --text-large-semi-bold-line-height: 150%;
  --text-medium-normal-font-family: "Lexend Deca", Helvetica;
  --text-medium-normal-font-size: 18px;
  --text-medium-normal-font-style: normal;
  --text-medium-normal-font-weight: 400;
  --text-medium-normal-letter-spacing: 0px;
  --text-medium-normal-line-height: 150%;
  --text-medium-semi-bold-font-family: "Lexend Deca", Helvetica;
  --text-medium-semi-bold-font-size: 18px;
  --text-medium-semi-bold-font-style: normal;
  --text-medium-semi-bold-font-weight: 600;
  --text-medium-semi-bold-letter-spacing: 0px;
  --text-medium-semi-bold-line-height: 150%;
  --text-regular-link-font-family: "Lexend Deca", Helvetica;
  --text-regular-link-font-size: 16px;
  --text-regular-link-font-style: normal;
  --text-regular-link-font-weight: 400;
  --text-regular-link-letter-spacing: 0px;
  --text-regular-link-line-height: 150%;
  --text-regular-medium-font-family: "Lexend Deca", Helvetica;
  --text-regular-medium-font-size: 16px;
  --text-regular-medium-font-style: normal;
  --text-regular-medium-font-weight: 500;
  --text-regular-medium-letter-spacing: 0px;
  --text-regular-medium-line-height: 150%;
  --text-regular-normal-font-family: "Lexend Deca", Helvetica;
  --text-regular-normal-font-size: 16px;
  --text-regular-normal-font-style: normal;
  --text-regular-normal-font-weight: 400;
  --text-regular-normal-letter-spacing: 0px;
  --text-regular-normal-line-height: 150%;
  --text-regular-semi-bold-font-family: "Lexend Deca", Helvetica;
  --text-regular-semi-bold-font-size: 16px;
  --text-regular-semi-bold-font-style: normal;
  --text-regular-semi-bold-font-weight: 600;
  --text-regular-semi-bold-letter-spacing: 0px;
  --text-regular-semi-bold-line-height: 150%;
  --text-small-link-font-family: "Lexend Deca", Helvetica;
  --text-small-link-font-size: 14px;
  --text-small-link-font-style: normal;
  --text-small-link-font-weight: 400;
  --text-small-link-letter-spacing: 0px;
  --text-small-link-line-height: 150%;
  --text-small-normal-font-family: "Lexend Deca", Helvetica;
  --text-small-normal-font-size: 14px;
  --text-small-normal-font-style: normal;
  --text-small-normal-font-weight: 400;
  --text-small-normal-letter-spacing: 0px;
  --text-small-normal-line-height: 150%;
  --text-small-semi-bold-font-family: "Lexend Deca", Helvetica;
  --text-small-semi-bold-font-size: 14px;
  --text-small-semi-bold-font-style: normal;
  --text-small-semi-bold-font-weight: 600;
  --text-small-semi-bold-letter-spacing: 0px;
  --text-small-semi-bold-line-height: 150%;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: oklch(0.99 0 0);
    --foreground: oklch(0 0 0);

    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.44 0 0);

    --popover: oklch(0.99 0 0);
    --popover-foreground: oklch(0 0 0);

    --border: oklch(0.92 0 0);
    --input: oklch(0.94 0 0);

    --card: oklch(1 0 0);
    --card-foreground: oklch(0 0 0);

    --primary: oklch(0.5845 0.2238 27.3147);
    --primary-foreground: oklch(1 0 0);

    --secondary: oklch(0.94 0 0);
    --secondary-foreground: oklch(0 0 0);

    --accent: oklch(0.94 0 0);
    --accent-foreground: oklch(0 0 0);

    --destructive: oklch(0.63 0.19 23.03);
    --destructive-foreground: oklch(1 0 0);

    --ring: oklch(0 0 0);

    --radius: 0.5rem;

    --chart-1: oklch(0.81 0.17 75.35);

    --chart-2: oklch(0.55 0.22 264.53);

    --chart-3: oklch(0.72 0 0);

    --chart-4: oklch(0.92 0 0);

    --chart-5: oklch(0.56 0 0);

    --sidebar: oklch(0.99 0 0);

    --sidebar-foreground: oklch(0 0 0);

    --sidebar-primary: oklch(0 0 0);

    --sidebar-primary-foreground: oklch(1 0 0);

    --sidebar-accent: oklch(0.94 0 0);

    --sidebar-accent-foreground: oklch(0 0 0);

    --sidebar-border: oklch(0.94 0 0);

    --sidebar-ring: oklch(0 0 0);

    --font-sans: Geist, sans-serif;

    --font-serif: Georgia, serif;

    --font-mono: Geist Mono, monospace;

    --shadow-color: hsl(0 0% 0%);

    --shadow-opacity: 0.18;

    --shadow-blur: 2px;

    --shadow-spread: 0px;

    --shadow-offset-x: 0px;

    --shadow-offset-y: 1px;

    --letter-spacing: 0em;

    --spacing: 0.25rem;

    --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);

    --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);

    --shadow-sm: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 1px 2px -1px hsl(0 0% 0% / 0.18);

    --shadow: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 1px 2px -1px hsl(0 0% 0% / 0.18);

    --shadow-md: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 2px 4px -1px hsl(0 0% 0% / 0.18);

    --shadow-lg: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 4px 6px -1px hsl(0 0% 0% / 0.18);

    --shadow-xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 8px 10px -1px hsl(0 0% 0% / 0.18);

    --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);

    --tracking-normal: 0em;
  }

  .dark {
    --background: oklch(0 0 0);
    --foreground: oklch(1 0 0);

    --muted: oklch(0.23 0 0);
    --muted-foreground: oklch(0.72 0 0);

    --accent: oklch(0.32 0 0);
    --accent-foreground: oklch(1 0 0);

    --popover: oklch(0.18 0 0);
    --popover-foreground: oklch(1 0 0);

    --border: oklch(0.26 0 0);
    --input: oklch(0.32 0 0);

    --card: oklch(0.14 0 0);
    --card-foreground: oklch(1 0 0);

    --primary: oklch(1 0 0);
    --primary-foreground: oklch(0 0 0);

    --secondary: oklch(0.25 0 0);
    --secondary-foreground: oklch(1 0 0);

    --destructive: oklch(0.69 0.2 23.91);
    --destructive-foreground: oklch(0 0 0);

    --ring: oklch(0.72 0 0);

    --radius: 0.5rem;

    --chart-1: oklch(0.81 0.17 75.35);

    --chart-2: oklch(0.58 0.21 260.84);

    --chart-3: oklch(0.56 0 0);

    --chart-4: oklch(0.44 0 0);

    --chart-5: oklch(0.92 0 0);

    --sidebar: oklch(0.18 0 0);

    --sidebar-foreground: oklch(1 0 0);

    --sidebar-primary: oklch(1 0 0);

    --sidebar-primary-foreground: oklch(0 0 0);

    --sidebar-accent: oklch(0.32 0 0);

    --sidebar-accent-foreground: oklch(1 0 0);

    --sidebar-border: oklch(0.32 0 0);

    --sidebar-ring: oklch(0.72 0 0);

    --font-sans: Geist, sans-serif;

    --font-serif: Georgia, serif;

    --font-mono: Geist Mono, monospace;

    --shadow-color: hsl(0 0% 0%);

    --shadow-opacity: 0.18;

    --shadow-blur: 2px;

    --shadow-spread: 0px;

    --shadow-offset-x: 0px;

    --shadow-offset-y: 1px;

    --letter-spacing: 0em;

    --spacing: 0.25rem;

    --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);

    --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);

    --shadow-sm: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 1px 2px -1px hsl(0 0% 0% / 0.18);

    --shadow: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 1px 2px -1px hsl(0 0% 0% / 0.18);

    --shadow-md: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 2px 4px -1px hsl(0 0% 0% / 0.18);

    --shadow-lg: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 4px 6px -1px hsl(0 0% 0% / 0.18);

    --shadow-xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.18),
      0px 8px 10px -1px hsl(0 0% 0% / 0.18);

    --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);
  }
  .theme {
    --font-sans: Geist, sans-serif;
    --font-mono: Geist Mono, monospace;
    --font-serif: Georgia, serif;
    --radius: 0.5rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
