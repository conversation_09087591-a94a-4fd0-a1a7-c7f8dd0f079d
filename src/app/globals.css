@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --heading-h1-font-family: var(--font-archivo), "Archivo", Helvetica,
    sans-serif;
  --heading-h1-font-size: clamp(2rem, 5vw, 3.5rem);
  --heading-h1-font-style: normal;
  --heading-h1-font-weight: 400;
  --heading-h1-letter-spacing: -0.02em;
  --heading-h1-line-height: 1.2;

  --heading-h2-font-family: var(--font-archivo), "Archivo", Helvetica,
    sans-serif;
  --heading-h2-font-size: clamp(1.75rem, 4vw, 3rem);
  --heading-h2-font-style: normal;
  --heading-h2-font-weight: 400;
  --heading-h2-letter-spacing: -0.01em;
  --heading-h2-line-height: 1.2;

  --heading-h4-font-family: var(--font-archivo), "Archivo", Helvetica,
    sans-serif;
  --heading-h4-font-size: clamp(1.25rem, 2.5vw, 2rem);
  --heading-h4-font-style: normal;
  --heading-h4-font-weight: 400;
  --heading-h4-letter-spacing: -0.01em;
  --heading-h4-line-height: 1.3;

  --heading-h5-font-family: var(--font-archivo), "Archivo", Helvetica,
    sans-serif;
  --heading-h5-font-size: clamp(1.125rem, 2vw, 1.5rem);
  --heading-h5-font-style: normal;
  --heading-h5-font-weight: 400;
  --heading-h5-letter-spacing: -0.01em;
  --heading-h5-line-height: 1.4;

  --heading-tagline-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --heading-tagline-font-size: 1rem;
  --heading-tagline-font-style: normal;
  --heading-tagline-font-weight: 600;
  --heading-tagline-letter-spacing: 0px;
  --heading-tagline-line-height: 1.5;

  --text-large-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-large-semi-bold-font-size: clamp(1.125rem, 1.5vw, 1.25rem);
  --text-large-semi-bold-font-style: normal;
  --text-large-semi-bold-font-weight: 600;
  --text-large-semi-bold-letter-spacing: 0px;
  --text-large-semi-bold-line-height: 1.5;

  --text-medium-normal-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-medium-normal-font-size: clamp(1rem, 1.25vw, 1.125rem);
  --text-medium-normal-font-style: normal;
  --text-medium-normal-font-weight: 400;
  --text-medium-normal-letter-spacing: 0px;
  --text-medium-normal-line-height: 1.5;

  --text-medium-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-medium-semi-bold-font-size: clamp(1rem, 1.25vw, 1.125rem);
  --text-medium-semi-bold-font-style: normal;
  --text-medium-semi-bold-font-weight: 600;
  --text-medium-semi-bold-letter-spacing: 0px;
  --text-medium-semi-bold-line-height: 1.5;

  --text-regular-link-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-regular-link-font-size: 1rem;
  --text-regular-link-font-style: normal;
  --text-regular-link-font-weight: 400;
  --text-regular-link-letter-spacing: 0px;
  --text-regular-link-line-height: 1.5;

  --text-regular-medium-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-regular-medium-font-size: 1rem;
  --text-regular-medium-font-style: normal;
  --text-regular-medium-font-weight: 500;
  --text-regular-medium-letter-spacing: 0px;
  --text-regular-medium-line-height: 1.5;

  --text-regular-normal-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-regular-normal-font-size: 1rem;
  --text-regular-normal-font-style: normal;
  --text-regular-normal-font-weight: 400;
  --text-regular-normal-letter-spacing: 0px;
  --text-regular-normal-line-height: 1.5;

  --text-regular-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-regular-semi-bold-font-size: 1rem;
  --text-regular-semi-bold-font-style: normal;
  --text-regular-semi-bold-font-weight: 600;
  --text-regular-semi-bold-letter-spacing: 0px;
  --text-regular-semi-bold-line-height: 1.5;

  --text-small-link-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-small-link-font-size: 0.875rem;
  --text-small-link-font-style: normal;
  --text-small-link-font-weight: 400;
  --text-small-link-letter-spacing: 0px;
  --text-small-link-line-height: 1.5;

  --text-small-normal-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-small-normal-font-size: 0.875rem;
  --text-small-normal-font-style: normal;
  --text-small-normal-font-weight: 400;
  --text-small-normal-letter-spacing: 0px;
  --text-small-normal-line-height: 1.5;

  --text-small-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca",
    Helvetica, sans-serif;
  --text-small-semi-bold-font-size: 0.875rem;
  --text-small-semi-bold-font-style: normal;
  --text-small-semi-bold-font-weight: 600;
  --text-small-semi-bold-letter-spacing: 0px;
  --text-small-semi-bold-line-height: 1.5;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: transparent;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --card: transparent;
    --card-foreground: 213 31% 91%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --ring: 216 34% 17%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-16 sm:py-20 lg:py-28;
  }

  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-12;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Carousel specific styles */
.embla {
  overflow: hidden;
}

.embla__container {
  display: flex;
}

.embla__slide {
  flex: 0 0 auto;
  min-width: 0;
}

/* Enhanced Mobile menu animations */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%) translateY(-50%) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateX(0) translateY(-50%) scale(1);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0) translateY(-50%) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(100%) translateY(-50%) scale(0.95);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes zoomIn {
  from {
    transform: scale(0.95);
  }
  to {
    transform: scale(1);
  }
}

@keyframes zoomOut {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.95);
  }
}

.mobile-menu-enter {
  animation: slideInFromRight 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.mobile-menu-exit {
  animation: slideOutToRight 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Smooth scrolling for mobile */
@media (max-width: 768px) {
  html {
    scroll-behavior: smooth;
  }
}

/* Touch-friendly button sizes */
@media (max-width: 768px) {
  button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Custom backdrop blur for mobile menu */
.mobile-menu-backdrop {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.1);
}

/* Typography Utility Classes */

/* Main Headings (H1, H2) - Manrope */
.font-heading {
  font-family: var(--font-manrope);
  font-weight: 600; /* Semibold */
}

.font-heading-bold {
  font-family: var(--font-manrope);
  font-weight: 700; /* Bold */
}

/* Subheadings (H3, H4) - IBM Plex Sans */
.font-subheading {
  font-family: var(--font-ibm-plex-sans);
  font-weight: 500; /* Medium */
}

/* Navigation - Montserrat */
.font-nav {
  font-family: var(--font-montserrat);
  font-weight: 600; /* Semi-Bold */
}

/* Body Text - Inter */
.font-body {
  font-family: var(--font-inter);
  font-weight: 400; /* Regular */
}

/* CTAs (Buttons, Links) - Poppins */
.font-cta {
  font-family: var(--font-poppins);
  font-weight: 600; /* Semi-Bold */
}

.font-cta-bold {
  font-family: var(--font-poppins);
  font-weight: 700; /* Bold */
}

/* Product Specs / Labels - Space Grotesk */
.font-specs {
  font-family: var(--font-space-grotesk);
  font-weight: 400; /* Regular */
}

.font-specs-medium {
  font-family: var(--font-space-grotesk);
  font-weight: 500; /* Medium */
}

/* Footnotes/Legal - Roboto */
.font-footnote {
  font-family: var(--font-roboto);
  font-weight: 300; /* Light */
}

.font-legal {
  font-family: var(--font-roboto);
  font-weight: 400; /* Regular */
}
